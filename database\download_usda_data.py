#!/usr/bin/env python3
"""
USDA FoodData Central 数据下载脚本
下载完整的食物营养数据库并准备MySQL导入
"""

import os
import requests
import zipfile
from pathlib import Path
import logging
import time
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class USDADataDownloader:
    def __init__(self, download_dir="./data"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)

        # 配置网络会话和重试机制
        self.session = self._create_session()
        
        # USDA FoodData Central 最新数据下载链接 (根据官方页面更新)
        self.download_urls = {
            "foundation_foods": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_csv_2025-04-24.zip",
                "json": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_foundation_food_json_2025-04.zip"
            }
        }

    def _create_session(self):
        """创建带有重试机制和代理处理的网络会话"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=5,  # 总重试次数
            backoff_factor=2,  # 重试间隔倍数
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
            allowed_methods=["HEAD", "GET", "OPTIONS"]  # 允许重试的HTTP方法
        )

        # 创建HTTP适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # 设置请求头
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 禁用SSL警告（仅在必要时使用）
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 设置超时时间
        session.timeout = (30, 300)  # (连接超时, 读取超时)

        return session

    def download_file(self, url, filename):
        """下载文件并显示进度，支持重试和代理处理"""
        file_path = self.download_dir / filename

        if file_path.exists():
            logger.info(f"文件已存在: {filename}")
            return file_path

        logger.info(f"开始下载: {filename}")
        logger.info(f"URL: {url}")

        # 尝试多种下载方式
        download_methods = [
            self._download_with_session,
            self._download_with_verify_false,
            self._download_with_urllib
        ]

        for method_idx, download_method in enumerate(download_methods, 1):
            try:
                logger.info(f"尝试下载方法 {method_idx}/{len(download_methods)}")
                result = download_method(url, file_path)
                if result:
                    logger.info(f"下载完成: {filename}")
                    return file_path

            except Exception as e:
                logger.warning(f"下载方法 {method_idx} 失败: {e}")
                if method_idx < len(download_methods):
                    logger.info(f"等待 3 秒后尝试下一种方法...")
                    time.sleep(3)
                continue

        logger.error(f"所有下载方法都失败了: {filename}")
        return None

    def _download_with_session(self, url, file_path):
        """使用配置好的session下载"""
        response = self.session.get(url, stream=True, timeout=(30, 300))
        response.raise_for_status()
        return self._save_response_to_file(response, file_path)

    def _download_with_verify_false(self, url, file_path):
        """禁用SSL验证的下载方式"""
        response = self.session.get(url, stream=True, verify=False, timeout=(30, 300))
        response.raise_for_status()
        return self._save_response_to_file(response, file_path)

    def _download_with_urllib(self, url, file_path):
        """使用urllib作为备用下载方式"""
        import urllib.request
        import urllib.error

        # 创建请求对象
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

        # 下载文件
        with urllib.request.urlopen(req, timeout=300) as response:
            total_size = int(response.headers.get('Content-Length', 0))
            downloaded_size = 0

            with open(file_path, 'wb') as f:
                while True:
                    chunk = response.read(8192)
                    if not chunk:
                        break
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')

        print()  # 换行
        return True

    def _save_response_to_file(self, response, file_path):
        """将响应内容保存到文件"""
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')

        print()  # 换行
        return True

    def diagnose_network(self):
        """诊断网络连接状况"""
        logger.info("开始网络连接诊断...")

        # 测试基本网络连接
        test_urls = [
            "https://www.google.com",
            "https://fdc.nal.usda.gov",
            "https://httpbin.org/get"
        ]

        for url in test_urls:
            try:
                response = self.session.head(url, timeout=10)
                logger.info(f"✓ 连接成功: {url} (状态码: {response.status_code})")
            except Exception as e:
                logger.warning(f"✗ 连接失败: {url} - {e}")

        # 检查代理设置
        import os
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_vars:
            value = os.environ.get(var)
            if value:
                logger.info(f"检测到代理设置: {var}={value}")

        logger.info("网络诊断完成")

    def verify_download_urls(self):
        """验证下载URL的可用性"""
        logger.info("验证下载URL可用性...")

        for dataset_name, urls in self.download_urls.items():
            logger.info(f"\n检查数据集: {dataset_name}")

            for format_type, url in urls.items():
                try:
                    response = self.session.head(url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"  ✓ {format_type.upper()}: URL可用 ({url})")
                    else:
                        logger.warning(f"  ✗ {format_type.upper()}: HTTP {response.status_code} ({url})")
                except Exception as e:
                    logger.error(f"  ✗ {format_type.upper()}: 连接失败 - {e}")

                    # 提供可能的解决方案
                    if "404" in str(e) or "Not Found" in str(e):
                        logger.info(f"    建议: 访问 https://fdc.nal.usda.gov/download-datasets 查看最新链接")

    def extract_zip(self, zip_path, extract_dir=None):
        """解压ZIP文件"""
        if extract_dir is None:
            extract_dir = self.download_dir / "extracted"
        
        extract_dir = Path(extract_dir)
        extract_dir.mkdir(exist_ok=True)
        
        logger.info(f"解压文件: {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            logger.info(f"解压完成到: {extract_dir}")
            return extract_dir
            
        except Exception as e:
            logger.error(f"解压失败: {e}")
            return None

    def download_full_dataset(self):
        """下载完整数据集"""
        logger.info("开始下载USDA FoodData Central完整数据集...")

        url = self.download_urls["full_download"]["csv"]
        filename = "FoodData_Central_csv_2025-04-24.zip"
        
        zip_path = self.download_file(url, filename)
        if zip_path:
            extract_dir = self.extract_zip(zip_path)
            return extract_dir
        return None

    def download_specific_dataset(self, dataset_type, format_type="csv"):
        """下载特定数据集"""
        if dataset_type not in self.download_urls:
            logger.error(f"未知的数据集类型: {dataset_type}")
            return None
            
        if format_type not in self.download_urls[dataset_type]:
            logger.error(f"数据集 {dataset_type} 不支持格式 {format_type}")
            return None
        
        url = self.download_urls[dataset_type][format_type]
        filename = f"{dataset_type}_{format_type}_2025-04.zip"
        
        zip_path = self.download_file(url, filename)
        if zip_path:
            extract_dir = self.extract_zip(zip_path)
            return extract_dir
        return None

    def list_csv_files(self, directory):
        """列出目录中的所有CSV文件"""
        csv_files = list(Path(directory).glob("**/*.csv"))
        logger.info(f"找到 {len(csv_files)} 个CSV文件:")
        for csv_file in csv_files:
            file_size = csv_file.stat().st_size / (1024 * 1024)  # MB
            logger.info(f"  - {csv_file.name} ({file_size:.1f} MB)")
        return csv_files

def main():
    """主函数"""
    downloader = USDADataDownloader()

    print("USDA FoodData Central 数据下载器")
    print("=" * 50)
    print("1. 下载完整数据集 (推荐, ~3GB)")
    print("2. 下载Foundation Foods (基础食物)")
    print("3. 下载SR Legacy (标准参考)")
    print("4. 下载FNDDS (膳食研究)")
    print("5. 下载Branded Foods (品牌食品)")
    print("6. 网络连接诊断")
    print("7. 验证下载URL可用性")

    choice = input("\n请选择选项 (1-7): ").strip()
    
    if choice == "1":
        extract_dir = downloader.download_full_dataset()
    elif choice == "2":
        extract_dir = downloader.download_specific_dataset("foundation_foods")
    elif choice == "3":
        extract_dir = downloader.download_specific_dataset("sr_legacy")
    elif choice == "4":
        extract_dir = downloader.download_specific_dataset("fndds")
    elif choice == "5":
        extract_dir = downloader.download_specific_dataset("branded")
    elif choice == "6":
        downloader.diagnose_network()
        return
    elif choice == "7":
        downloader.verify_download_urls()
        return
    else:
        print("无效选择")
        return
    
    if extract_dir:
        print(f"\n数据已下载并解压到: {extract_dir}")
        csv_files = downloader.list_csv_files(extract_dir)
        print(f"\n接下来可以使用 mysql_import.py 脚本将这些CSV文件导入MySQL数据库")

if __name__ == "__main__":
    main()
