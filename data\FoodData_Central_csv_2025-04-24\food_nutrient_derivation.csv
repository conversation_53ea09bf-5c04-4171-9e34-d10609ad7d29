"id","code","description"
"1","A","Analytical"
"2","AI","Analytical data; from the literature or  government;  incomplete documentation"
"3","AR","Analytical data; derived by linear regression"
"4","AS","Summed"
"5","BD","Based on same food; Drained solids from solids and liquids or vice versa (canned fruits and vegetables)"
"6","BFAN","Based on another form of the food or similar food; Concentration adjustment; Ash; Retention factors not used"
"7","BFAY","Based on another form of the food or similar food; Concentration adjustment; Ash; Retention factors used"
"8","BFCN","Based on another form of the food or similar food; Concentration adjustment; Carbohydrate; Retention factors not used"
"9","BFCY","Based on another form of the food or similar food; Concentration adjustment; Carbohydrate; Retention factors used"
"10","BFFN","Based on another form of the food or similar food; Concentration adjustment; Fat; Retention factors not used"
"11","BFFY","Based on another form of the food or similar food; Concentration adjustment; Fat; Retention factors used"
"12","BFNN","Based on another form of the food or similar food; Concentration adjustment; Non-fat solids; Retention factors not used"
"13","BFNY","Based on another form of the food or similar food; Concentration adjustment; Non-fat solids; Retentions factors used"
"14","BFPN","Based on another form of the food or similar food; Concentration adjustment; Protein; Retention factors not used"
"15","BFPY","Based on another form of the food or similar food; Concentration adjustment; Protein; Retention factors used"
"16","BFSN","Based on another form of the food or similar food; Concentration adjustment; Solids; Retention factors not used"
"17","BFSY","Based on another form of the food or similar food; Concentration adjustment; Solids; Retention factors used"
"18","BFYN","Based on another form of the food or similar food; Concentration adjustment; Yield; Retention factors not used"
"19","BFYY","Based on another form of the food or similar food; Concentration adjustment; Yield; Retention factors used"
"20","BFZN","Based on another form of the food or similar food; Concentration adjustment; No adjustment; Retention factors not used"
"21","BFZY","Based on another form of the food or similar food; Concentration adjustment; No adjustment; Retention factors used"
"22","BNA","Based on another form of the same food or similar food: constituents normalized to total; vitamin A"
"23","CAAN","Calculated from different food; From average values for food category; Ash; Retention factors not used"
"24","CAAY","Calculated from different food; From average values for food category; Ash; Retention factors used"
"25","CACN","Calculated from different food; From average values for food category; Carbohydrate; Retention factors not used"
"26","CACY","Calculated from different food; From average values for food category; Carbohydrate; Retention factors used"
"27","CAFN","Calculated from different food; From average values for food category; Fat; Retention factors not used"
"28","CAFY","Calculated from different food; From average values for food category; Fat; Retention factors used"
"29","CANN","Calculated from different food; From average values for food category; Non-fat solids; Retention factors not used"
"30","CANY","Calculated from different food; From average values for food category; Non-fat solids; Retention factors used"
"31","CAPN","Calculated from different food; From average values for food category; Protein; Retention factors not used"
"32","CAPY","Calculated from different food; From average values for food category; Protein; Retention factors used"
"33","CASN","Calculated from different food; From average values for food category; Solids; Retention factors not used"
"34","CASY","Calculated from different food; From average values for food category; Solids; Retention factors used"
"35","CAZN","Calculated from different food; From average values for food category; No adjustment; Retention factors not used"
"36","CAZY","Calculated from different food; From average values for food category; No adjustment; Retention factors used"
"37","DA","Concentration adjustment using factor; derived from analytical data"
"38","DI","Concentration adjustment using factor; derived from imputed data"
"39","FLA","Estimated formulation based on ingredient list; Linear program used to estimate ingredients; Analytical data"
"40","FLC","Estimated formulation based on ingredient list; Linear program used to estimate ingredients; Claim on label/serving"
"41","FLM","Estimated formulation based on ingredient list; Linear program used to estimate ingredients; Manuf. Calc. data/100"
"42","JA","Aggregated data involving combinations of data with only source codes 1 and 12 and/or 13"
"43","JO","Aggregated data involving combinations of data with different source codes when at least one code is not 1, 6, 12, or 13"
"44","LC","Label claim (back calculated from label by NDL staff; Calculated from label claim/serving (g or %RDI)"
"45","LCGS","Label Claim Global Synchronization"
"46","MA","Manufacturer supplied(industry or trade association), Analytical data, incomplete documentation"
"47","MC","Manufacturer supplied; Calculated by manufacturer or unknown if analytical or calculated"
"48","ML","Manufacturer supplied; Value upon which manufacturer based label claim for fortified/enriched nutrient"
"49","NC","Calculated"
"50","NP","Nutrient that is based on other nutrient/s; calculated by difference or summed (with or without activity factors) Ex. Proximate component other than CHO by difference. Vitamin A calculated from components when one of the component values is not source code 1 or 7"
"51","NR","Nutrient that is based on other nutrient/s; value used directly, ex. Nut.#204 from Nut.#298"
"52","O","Other procedure used from imputing"
"53","PAE","Based on physical composition; Derived from analytical data; Estimated physical composition"
"54","PAK","Based on physical composition; Derived from analytical data; Known physical composition"
"55","PIE","Based on physical composition; Derived from imputed data; Estimated physical composition"
"56","PIK","Based on physical composition; Derived from imputed data; Known physical composition"
"57","RA","Recipe; Approximate ingredient proportions (ex. combination of several recipes)"
"58","RC","Recipe; Cookbook"
"59","RF","Recipe; Formulary of standard products (formulary or standards of identity)"
"60","RK","Recipe; Known formulation (dissection data or proprietary formulation)"
"61","RKA","Recipe; Known formulation; No adjustments applied, combination of source codes 1, 12, and/or 6."
"62","RKI","Recipe;Known formulation;No adjustments applied, combination of source codes which includes codes other than 1,12,or 6"
"63","RP","Recipe; Per package directions (ex. refrigerated dough, toast, cake mix)"
"64","RPA","Recipe; Per package directions; No adjustments applied, combination of source codes 1, 12, and/or 6."
"65","RPI","Recipe;Per package directions;No adjustments applied, combination of source codes which incl codes other than 1,12,or 6"
"66","S","Product standard, such as enrichment level specified in CFR or AMS commodity standard"
"67","T","Taken from another source--other tables of food composition"
"68","Z","Assumed zero (Insignificant amount or not naturally occurring in a food, such as fiber in meat)"
"69","LCBF","Label Claim Branded Food Products"
"70","LCCS","Calculated from value per serving size measure"
"71","LCSA","Calculated from an approximate value per serving size measure"
"72","LCSE","Calculated from an exact value per serving size measure"
"73","LCSL","Calculated from a less than value per serving size measure"
"74","LCSG","Calculated from a greater than value per serving size measure"
"75","LCCD","Calculated from a daily value percentage per serving size measure"
"76","LCGP","Given by information provider as a value per 100 unit measure"
"77","LCGE","Given by information provider as an exact value per 100 unit measure"
"78","LCGA","Given by information provider as an approximate value per 100 unit measure"
"79","LCGL","Given by information provider as a less than value per 100 unit measure"
"80","LCGG","Given by information provider as a greater than value per 100 unit measure"
