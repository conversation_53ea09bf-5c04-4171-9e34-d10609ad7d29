#!/usr/bin/env python3
"""
USDA食物数据中文翻译管理脚本
提供批量翻译、翻译管理和查询功能
"""

import mysql.connector
from mysql.connector import Error
import logging
import re
import json
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class USDATranslationManager:
    def __init__(self, host='localhost', database='meals', user='root', password='021026'):
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
        
        # 常见食物翻译词典
        self.food_translations = {
            # 基础食物
            'apple': '苹果',
            'banana': '香蕉',
            'orange': '橙子',
            'chicken': '鸡肉',
            'beef': '牛肉',
            'pork': '猪肉',
            'fish': '鱼',
            'rice': '大米',
            'bread': '面包',
            'milk': '牛奶',
            'egg': '鸡蛋',
            'cheese': '奶酪',
            'potato': '土豆',
            'tomato': '番茄',
            'carrot': '胡萝卜',
            'onion': '洋葱',
            'garlic': '大蒜',
            'spinach': '菠菜',
            'broccoli': '西兰花',
            'lettuce': '生菜',
            
            # 烹饪方式
            'raw': '生的',
            'cooked': '熟的',
            'boiled': '水煮的',
            'fried': '油炸的',
            'grilled': '烤制的',
            'steamed': '蒸制的',
            'baked': '烘烤的',
            'roasted': '烘烤的',
            'fresh': '新鲜的',
            'frozen': '冷冻的',
            'canned': '罐装的',
            'dried': '干燥的',
            
            # 部位和形态
            'whole': '整个',
            'piece': '块',
            'slice': '片',
            'ground': '绞碎的',
            'chopped': '切碎的',
            'diced': '切丁的',
            'breast': '胸肉',
            'thigh': '大腿肉',
            'wing': '翅膀',
            'skin': '皮',
            'bone': '骨头',
            'lean': '瘦肉',
            'fat': '肥肉',
            
            # 品牌和类型
            'organic': '有机',
            'natural': '天然',
            'low fat': '低脂',
            'fat free': '无脂',
            'sugar free': '无糖',
            'sodium free': '无钠',
            'enriched': '强化的',
            'fortified': '添加营养的'
        }

    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                use_unicode=True,
                autocommit=False
            )
            
            if self.connection.is_connected():
                logger.info(f"成功连接到MySQL数据库: {self.database}")
                return True
                
        except Error as e:
            logger.error(f"连接MySQL失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("MySQL连接已关闭")

    def execute_sql_file(self, sql_file_path):
        """执行SQL文件"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # 分割SQL语句
            sql_commands = sql_script.split(';')
            cursor = self.connection.cursor()
            
            for command in sql_commands:
                command = command.strip()
                if command:
                    try:
                        cursor.execute(command)
                    except Error as e:
                        logger.warning(f"执行SQL命令时出现警告: {e}")
            
            self.connection.commit()
            cursor.close()
            logger.info(f"SQL文件执行完成: {sql_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"执行SQL文件失败: {e}")
            return False

    def simple_translate(self, english_text):
        """简单的英文到中文翻译"""
        if not english_text:
            return english_text
        
        # 转换为小写进行匹配
        text_lower = english_text.lower()
        translated_parts = []
        
        # 分词并翻译
        words = re.findall(r'\b\w+\b', text_lower)
        for word in words:
            if word in self.food_translations:
                translated_parts.append(self.food_translations[word])
            else:
                translated_parts.append(word)
        
        # 如果有翻译，组合结果
        if any(part in self.food_translations.values() for part in translated_parts):
            return ''.join(translated_parts)
        
        # 如果没有找到翻译，返回原文
        return english_text

    def batch_translate_foods(self, limit=1000):
        """批量翻译食物名称"""
        try:
            cursor = self.connection.cursor()
            
            # 获取未翻译的食物
            query = """
            SELECT f.fdc_id, f.description 
            FROM usda_foods f 
            LEFT JOIN usda_food_translations ft ON f.fdc_id = ft.fdc_id 
            WHERE ft.fdc_id IS NULL 
            LIMIT %s
            """
            cursor.execute(query, (limit,))
            foods = cursor.fetchall()
            
            logger.info(f"开始翻译 {len(foods)} 个食物名称...")
            
            # 准备插入语句
            insert_query = """
            INSERT INTO usda_food_translations 
            (fdc_id, chinese_name, translation_source, confidence_score) 
            VALUES (%s, %s, %s, %s)
            """
            
            translated_count = 0
            for fdc_id, description in foods:
                chinese_name = self.simple_translate(description)
                
                # 如果翻译结果与原文不同，说明有翻译
                if chinese_name != description:
                    confidence = 0.7  # 简单翻译的置信度
                    source = '自动'
                else:
                    confidence = 0.3  # 未翻译的置信度较低
                    source = '自动'
                    chinese_name = description  # 保持原文
                
                try:
                    cursor.execute(insert_query, (fdc_id, chinese_name, source, confidence))
                    translated_count += 1
                except Error as e:
                    logger.warning(f"插入翻译失败 {fdc_id}: {e}")
            
            self.connection.commit()
            cursor.close()
            logger.info(f"批量翻译完成，成功翻译 {translated_count} 个食物名称")
            return translated_count
            
        except Exception as e:
            logger.error(f"批量翻译失败: {e}")
            return 0

    def add_manual_translation(self, fdc_id, chinese_name, chinese_description=None):
        """添加手动翻译"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已存在翻译
            check_query = "SELECT id FROM usda_food_translations WHERE fdc_id = %s"
            cursor.execute(check_query, (fdc_id,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有翻译
                update_query = """
                UPDATE usda_food_translations 
                SET chinese_name = %s, chinese_description = %s, 
                    translation_source = '手动', confidence_score = 1.0, verified = TRUE
                WHERE fdc_id = %s
                """
                cursor.execute(update_query, (chinese_name, chinese_description, fdc_id))
                logger.info(f"更新翻译: {fdc_id} -> {chinese_name}")
            else:
                # 插入新翻译
                insert_query = """
                INSERT INTO usda_food_translations 
                (fdc_id, chinese_name, chinese_description, translation_source, confidence_score, verified) 
                VALUES (%s, %s, %s, '手动', 1.0, TRUE)
                """
                cursor.execute(insert_query, (fdc_id, chinese_name, chinese_description))
                logger.info(f"添加翻译: {fdc_id} -> {chinese_name}")
            
            self.connection.commit()
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"添加手动翻译失败: {e}")
            return False

    def search_foods_chinese(self, keyword, limit=20):
        """使用中文关键词搜索食物"""
        try:
            cursor = self.connection.cursor()
            
            query = """
            SELECT fdc_id, english_name, chinese_name, chinese_category, translation_verified
            FROM usda_foods_chinese 
            WHERE chinese_name LIKE %s OR chinese_description LIKE %s
            ORDER BY translation_verified DESC, confidence_score DESC
            LIMIT %s
            """
            
            search_pattern = f"%{keyword}%"
            cursor.execute(query, (search_pattern, search_pattern, limit))
            results = cursor.fetchall()
            
            cursor.close()
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []

    def get_translation_stats(self):
        """获取翻译统计信息"""
        try:
            cursor = self.connection.cursor()
            
            # 总食物数量
            cursor.execute("SELECT COUNT(*) FROM usda_foods")
            total_foods = cursor.fetchone()[0]
            
            # 已翻译数量
            cursor.execute("SELECT COUNT(*) FROM usda_food_translations")
            translated_foods = cursor.fetchone()[0]
            
            # 已验证翻译数量
            cursor.execute("SELECT COUNT(*) FROM usda_food_translations WHERE verified = TRUE")
            verified_translations = cursor.fetchone()[0]
            
            # 按来源统计
            cursor.execute("""
                SELECT translation_source, COUNT(*) 
                FROM usda_food_translations 
                GROUP BY translation_source
            """)
            source_stats = cursor.fetchall()
            
            cursor.close()
            
            stats = {
                'total_foods': total_foods,
                'translated_foods': translated_foods,
                'verified_translations': verified_translations,
                'translation_rate': (translated_foods / total_foods * 100) if total_foods > 0 else 0,
                'verification_rate': (verified_translations / translated_foods * 100) if translated_foods > 0 else 0,
                'source_stats': dict(source_stats)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    print("USDA食物数据中文翻译管理工具")
    print("=" * 50)
    
    # 数据库连接配置
    db_config = {
        'host': input("MySQL主机 (默认: localhost): ").strip() or 'localhost',
        'user': input("MySQL用户名 (默认: root): ").strip() or 'root',
        'password': input("MySQL密码: ").strip(),
        'database': input("数据库名 (默认: meals): ").strip() or 'meals'
    }
    
    # 创建翻译管理器
    manager = USDATranslationManager(**db_config)
    
    # 连接数据库
    if not manager.connect():
        return
    
    try:
        # 执行翻译架构
        schema_file = Path("database/usda_translation_schema.sql")
        if not schema_file.exists():
            schema_file = Path("usda_translation_schema.sql")
        
        if schema_file.exists():
            print("创建翻译表架构...")
            manager.execute_sql_file(schema_file)
        
        while True:
            print("\n请选择操作:")
            print("1. 批量翻译食物名称")
            print("2. 添加手动翻译")
            print("3. 搜索食物")
            print("4. 查看翻译统计")
            print("5. 退出")
            
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == "1":
                limit = int(input("翻译数量限制 (默认: 1000): ").strip() or "1000")
                count = manager.batch_translate_foods(limit)
                print(f"成功翻译 {count} 个食物名称")
                
            elif choice == "2":
                fdc_id = int(input("请输入USDA食物ID: ").strip())
                chinese_name = input("请输入中文名称: ").strip()
                chinese_desc = input("请输入中文描述 (可选): ").strip() or None
                
                if manager.add_manual_translation(fdc_id, chinese_name, chinese_desc):
                    print("翻译添加成功")
                else:
                    print("翻译添加失败")
                    
            elif choice == "3":
                keyword = input("请输入搜索关键词: ").strip()
                results = manager.search_foods_chinese(keyword)
                
                print(f"\n搜索结果 ({len(results)} 条):")
                for fdc_id, english, chinese, category, verified in results:
                    status = "✓" if verified else "?"
                    print(f"{status} {fdc_id}: {chinese} ({english}) - {category}")
                    
            elif choice == "4":
                stats = manager.get_translation_stats()
                print(f"\n翻译统计:")
                print(f"总食物数量: {stats.get('total_foods', 0):,}")
                print(f"已翻译数量: {stats.get('translated_foods', 0):,}")
                print(f"翻译覆盖率: {stats.get('translation_rate', 0):.1f}%")
                print(f"验证覆盖率: {stats.get('verification_rate', 0):.1f}%")
                print(f"翻译来源统计: {stats.get('source_stats', {})}")
                
            elif choice == "5":
                break
            else:
                print("无效选择")
    
    finally:
        manager.disconnect()

if __name__ == "__main__":
    main()
