package com.example.meals.dto;

import com.example.meals.entity.ChinaFood;

import java.math.BigDecimal;

/**
 * 食物搜索响应DTO
 */
public class FoodSearchResponse {
    
    private Long id;
    private String foodCode;
    private String foodName;
    private Integer energyKcal;
    private BigDecimal protein;
    private BigDecimal fat;
    private BigDecimal cho;
    private String category;
    
    public FoodSearchResponse() {}
    
    public FoodSearchResponse(ChinaFood food) {
        this.id = food.getId();
        this.foodCode = food.getFoodCode();
        this.foodName = food.getFoodName();
        this.energyKcal = food.getEnergyKcal();
        this.protein = food.getProtein();
        this.fat = food.getFat();
        this.cho = food.getCho();
        
        // 根据食物代码前两位确定分类
        if (food.getFoodCode() != null && food.getFoodCode().length() >= 2) {
            String categoryCode = food.getFoodCode().substring(0, 2);
            this.category = getCategoryName(categoryCode);
        }
    }
    
    private String getCategoryName(String categoryCode) {
        switch (categoryCode) {
            case "01": return "谷类及其制品";
            case "02": return "薯类、淀粉及其制品";
            case "03": return "干豆类及其制品";
            case "04": return "蔬菜类及其制品";
            case "05": return "菌藻类";
            case "06": return "水果类及其制品";
            case "07": return "坚果、种子类";
            case "08": return "畜肉类及其制品";
            case "09": return "禽肉类及其制品";
            case "10": return "乳类及其制品";
            case "11": return "蛋类及其制品";
            case "12": return "鱼虾蟹贝类";
            case "13": return "调料类";
            case "14": return "饮料类";
            case "15": return "酒类";
            case "16": return "其他";
            case "19": return "油脂类";
            case "21": return "特殊食品类";
            default: return "未知分类";
        }
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFoodCode() {
        return foodCode;
    }

    public void setFoodCode(String foodCode) {
        this.foodCode = foodCode;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public Integer getEnergyKcal() {
        return energyKcal;
    }

    public void setEnergyKcal(Integer energyKcal) {
        this.energyKcal = energyKcal;
    }

    public BigDecimal getProtein() {
        return protein;
    }

    public void setProtein(BigDecimal protein) {
        this.protein = protein;
    }

    public BigDecimal getFat() {
        return fat;
    }

    public void setFat(BigDecimal fat) {
        this.fat = fat;
    }

    public BigDecimal getCho() {
        return cho;
    }

    public void setCho(BigDecimal cho) {
        this.cho = cho;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
