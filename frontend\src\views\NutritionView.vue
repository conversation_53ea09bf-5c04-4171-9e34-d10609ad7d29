<template>
  <div class="nutrition-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      page-title="营养分析"
      back-to="/dashboard"
      back-text="返回控制台"
      :show-user-info="false"
    />

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 页面标题区域 -->
      <section class="page-header">
        <div class="header-content">
          <h1 class="page-title">营养分析</h1>
          <p class="page-subtitle">分析食物营养成分，制定健康饮食计划</p>
        </div>
      </section>

      <!-- 食物搜索区域 -->
      <section class="search-section">
        <div class="search-card">
          <h2 class="section-title">
            <span class="title-icon">🔍</span>
            食物搜索
          </h2>
          <div class="search-container">
            <div class="search-input-wrapper">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索食物名称，如：苹果、鸡蛋、牛奶..."
                class="search-input"
                @input="handleSearch"
                @keyup.enter="performSearch"
              />
              <button @click="performSearch" class="search-btn" :disabled="!searchQuery.trim() || isSearching">
                {{ isSearching ? '搜索中...' : '搜索' }}
              </button>
            </div>

            <!-- 搜索建议 -->
            <div v-if="searchSuggestions.length > 0" class="search-suggestions">
              <div
                v-for="suggestion in searchSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                @click="selectFood(suggestion)"
              >
                <span class="food-name">{{ suggestion.foodName }}</span>
                <span class="food-category">{{ suggestion.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 选中的食物信息 -->
      <section v-if="selectedFood" class="food-info-section">
        <div class="food-info-card">
          <div class="food-header">
            <h2 class="section-title">
              <span class="title-icon">🥗</span>
              {{ selectedFood.foodName }}
            </h2>
            <span class="food-category-badge">种类：{{ selectedFood.category }}</span>
          </div>

          <!-- 重量输入 -->
          <div class="weight-input-section">
            <label class="weight-label">食用重量：</label>
            <div class="weight-input-wrapper">
              <input
                v-model.number="foodWeight"
                type="number"
                min="1"
                max="9999"
                class="weight-input"
                @input="calculateNutrition"
              />
              <span class="weight-unit">克</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门食物推荐 -->
      <section v-if="!selectedFood && popularFoods.length > 0" class="popular-section">
        <div class="popular-card">
          <h2 class="section-title">
            <span class="title-icon">🔥</span>
            热门食物推荐
          </h2>
          <div class="popular-grid">
            <div
              v-for="food in popularFoods"
              :key="food.id"
              class="popular-item"
              @click="selectFood(food)"
            >
              <div class="popular-name">{{ food.foodName }}</div>
              <div class="popular-category">{{ food.category }}</div>
              <div class="popular-calories">{{ food.energyKcal }}千卡/100g</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 营养信息展示区域 -->
      <section v-if="selectedFood" class="nutrition-section">
        <div class="nutrition-card">
          <div class="nutrition-header">
            <h2 class="section-title">
              <span class="title-icon">📊</span>
              营养成分分析
            </h2>
            <button @click="clearSelection" class="clear-btn">重新选择</button>
          </div>
          <p class="nutrition-subtitle">基于 {{ foodWeight }}g {{ selectedFood.foodName }} 的营养成分</p>

          <!-- 加载状态 -->
          <div v-if="isAnalyzing" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在分析营养成分...</p>
          </div>

          <!-- 主要营养素 -->
          <div class="main-nutrients">
            <div class="nutrient-item primary">
              <div class="nutrient-icon">🔥</div>
              <div class="nutrient-info">
                <span class="nutrient-name">热量</span>
                <span class="nutrient-value">{{ nutritionAnalysis?.calculatedNutrition.calories || 0 }}</span>
                <span class="nutrient-unit">千卡</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🥩</div>
              <div class="nutrient-info">
                <span class="nutrient-name">蛋白质</span>
                <span class="nutrient-value">{{ nutritionAnalysis?.calculatedNutrition.protein || 0 }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🧈</div>
              <div class="nutrient-info">
                <span class="nutrient-name">脂肪</span>
                <span class="nutrient-value">{{ nutritionAnalysis?.calculatedNutrition.fat || 0 }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🍞</div>
              <div class="nutrient-info">
                <span class="nutrient-name">碳水化合物</span>
                <span class="nutrient-value">{{ nutritionAnalysis?.calculatedNutrition.carbohydrates || 0 }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>
          </div>

          <!-- 详细营养素 -->
          <div class="detailed-nutrients">
            <h3 class="detailed-title">详细营养成分</h3>
            <div class="nutrient-grid">
              <div class="nutrient-detail">
                <span class="detail-name">膳食纤维</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.fiber, 'g') }}</span>
              </div>
              <div class="nutrient-detail">
                <span class="detail-name">糖分</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.sugar, 'g') }}</span>
              </div>
              <div class="nutrient-detail">
                <span class="detail-name">钠</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.sodium, 'mg') }}</span>
              </div>
              <div class="nutrient-detail">
                <span class="detail-name">钙</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.calcium, 'mg') }}</span>
              </div>
              <div class="nutrient-detail">
                <span class="detail-name">铁</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.iron, 'mg') }}</span>
              </div>
              <div class="nutrient-detail">
                <span class="detail-name">维生素C</span>
                <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis?.calculatedNutrition.vitaminC, 'mg') }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 每日营养建议 -->
      <section class="recommendation-section">
        <div class="recommendation-card">
          <h2 class="section-title">
            <span class="title-icon">💡</span>
            每日营养建议
          </h2>
          <div class="recommendation-grid">
            <div class="recommendation-item">
              <div class="rec-icon">🔥</div>
              <div class="rec-content">
                <h4>成人每日热量需求</h4>
                <p>男性：2000-2500千卡<br>女性：1600-2000千卡</p>
              </div>
            </div>
            <div class="recommendation-item">
              <div class="rec-icon">🥩</div>
              <div class="rec-content">
                <h4>蛋白质建议摄入</h4>
                <p>每公斤体重需要0.8-1.2g蛋白质</p>
              </div>
            </div>
            <div class="recommendation-item">
              <div class="rec-icon">🥬</div>
              <div class="rec-content">
                <h4>膳食纤维建议</h4>
                <p>成人每日需要25-35g膳食纤维</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TopNavbar from '../components/TopNavbar.vue'
import {
  searchFoods,
  analyzeNutrition,
  getAllCategories,
  getPopularFoods,
  type FoodItem,
  type NutritionAnalysis,
  formatNutritionValue,
  getNutritionLevelColor
} from '../utils/foodApi'

// 路由
const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const searchSuggestions = ref<FoodItem[]>([])
const selectedFood = ref<FoodItem | null>(null)
const foodWeight = ref(100) // 默认100克
const isSearching = ref(false)
const isAnalyzing = ref(false)
const categories = ref<string[]>([])
const popularFoods = ref<FoodItem[]>([])
const nutritionAnalysis = ref<NutritionAnalysis | null>(null)

// 搜索处理函数
const handleSearch = async () => {
  if (searchQuery.value.trim().length < 2) {
    searchSuggestions.value = []
    return
  }

  try {
    isSearching.value = true
    const foods = await searchFoods(searchQuery.value.trim())
    searchSuggestions.value = foods.slice(0, 5)
  } catch (error) {
    console.error('搜索出错:', error)
    searchSuggestions.value = []
  } finally {
    isSearching.value = false
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) return
  handleSearch()
}

const selectFood = async (food: FoodItem) => {
  selectedFood.value = food
  searchQuery.value = food.foodName
  searchSuggestions.value = []
  await calculateNutrition()
}

const calculateNutrition = async () => {
  if (!selectedFood.value) return

  try {
    isAnalyzing.value = true
    const analysis = await analyzeNutrition(selectedFood.value.id, foodWeight.value)
    nutritionAnalysis.value = analysis
  } catch (error) {
    console.error('营养分析出错:', error)
    nutritionAnalysis.value = null
  } finally {
    isAnalyzing.value = false
  }
}

const clearSelection = () => {
  selectedFood.value = null
  nutritionAnalysis.value = null
  searchQuery.value = ''
  searchSuggestions.value = []
  foodWeight.value = 100
}

// 初始化数据
const initializeData = async () => {
  try {
    // 获取食物分类
    const categoriesData = await getAllCategories()
    categories.value = categoriesData

    // 获取热门食物
    const popularData = await getPopularFoods(6)
    popularFoods.value = popularData
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

onMounted(() => {
  initializeData()
})
</script>

<style scoped>
/* CSS变量定义 - 与项目保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* 全局样式 */
.nutrition-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 页面标题区域 */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  /* 移除渐变文字效果，使用普通颜色确保可见性 */
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

/* 通用卡片样式 */
.search-card,
.food-info-card,
.nutrition-card,
.recommendation-card,
.popular-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.title-icon {
  font-size: 1.25rem;
}

/* 搜索区域样式 */
.search-container {
  position: relative;
}

.search-input-wrapper {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #374151;
}

.search-input:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background: #16a085;
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.search-btn:hover:not(:disabled) {
  background: #138d75;
  transform: translateY(-1px);
}

.search-btn:disabled {
  background: #bdc3c7;
  color: #7f8c8d;
  cursor: not-allowed;
}

/* 搜索建议样式 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-medium);
  z-index: 10;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-color);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: var(--background-light);
}

.food-name {
  font-weight: 500;
  color: var(--text-primary);
}

.food-category {
  font-size: 0.875rem;
  color: var(--text-secondary);
  background: var(--background-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

/* 热门食物推荐样式 */
.popular-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.popular-item {
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.popular-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
  border-color: var(--primary-color);
}

.popular-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.popular-category {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.popular-calories {
  font-size: 0.875rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* 食物信息样式 */
.food-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.food-category-badge {
  background: var(--primary-color);
  color: rgb(12, 12, 12);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.weight-input-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
}

.weight-label {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.weight-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.weight-input {
  width: 100px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  text-align: center;
  font-weight: 500;
}

.weight-unit {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 营养信息样式 */
.nutrition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.clear-btn {
  padding: 0.5rem 1rem;
  background: var(--text-light);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #16a085;
  color: white;
}

.nutrition-subtitle {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-style: italic;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-nutrients {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.nutrient-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.nutrient-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.nutrient-item.primary {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white !important;
}

.nutrient-item.primary .nutrient-value,
.nutrient-item.primary .nutrient-name,
.nutrient-item.primary .nutrient-unit,
.nutrient-item.primary .nutrient-icon {
  color: white !important;
}

.nutrient-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.nutrient-item.primary .nutrient-icon {
  background: rgba(255, 255, 255, 0.3);
}

.nutrient-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nutrient-name {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
}

.nutrient-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.nutrient-unit {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* 详细营养成分样式 */
.detailed-nutrients {
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
}

.detailed-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.nutrient-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.nutrient-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.nutrient-detail:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(22, 160, 133, 0.1);
}

.detail-name {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.detail-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* 推荐区域样式 */
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.rec-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
  flex-shrink: 0;
}

.rec-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.rec-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .search-card,
  .food-info-card,
  .nutrition-card,
  .recommendation-card {
    padding: 1.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .main-nutrients {
    grid-template-columns: 1fr;
  }

  .nutrient-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .recommendation-grid {
    grid-template-columns: 1fr;
  }

  .search-input-wrapper {
    flex-direction: column;
  }

  .weight-input-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .nutrient-grid {
    grid-template-columns: 1fr;
  }

  .food-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
