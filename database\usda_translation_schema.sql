-- USDA食物数据中文化翻译表架构
-- 通过映射表方式实现USDA数据的中文显示

USE meals;

-- USDA食物中文翻译表
CREATE TABLE IF NOT EXISTS usda_food_translations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    chinese_name VARCHAR(255) NOT NULL COMMENT '中文名称',
    chinese_description TEXT COMMENT '中文描述',
    translation_source ENUM('手动', '自动', 'AI', '用户') DEFAULT '自动' COMMENT '翻译来源',
    confidence_score DECIMAL(3,2) COMMENT '翻译置信度(0-1)',
    verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    UNIQUE KEY uk_fdc_id (fdc_id),
    INDEX idx_chinese_name (chinese_name),
    FULLTEXT INDEX ft_chinese_name (chinese_name),
    FULLTEXT INDEX ft_chinese_description (chinese_description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物中文翻译表';

-- USDA食物分类中文翻译表
CREATE TABLE IF NOT EXISTS usda_category_translations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    category_id INT NOT NULL COMMENT 'USDA分类ID',
    chinese_name VARCHAR(255) NOT NULL COMMENT '中文分类名称',
    chinese_description TEXT COMMENT '中文分类描述',
    translation_source ENUM('手动', '自动', 'AI', '用户') DEFAULT '手动' COMMENT '翻译来源',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (category_id) REFERENCES usda_food_category(id) ON DELETE CASCADE,
    UNIQUE KEY uk_category_id (category_id),
    INDEX idx_chinese_name (chinese_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物分类中文翻译表';

-- USDA营养素中文翻译表
CREATE TABLE IF NOT EXISTS usda_nutrient_translations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    nutrient_id INT NOT NULL COMMENT 'USDA营养素ID',
    chinese_name VARCHAR(255) NOT NULL COMMENT '中文营养素名称',
    chinese_unit VARCHAR(50) COMMENT '中文单位',
    abbreviation VARCHAR(20) COMMENT '中文缩写',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (nutrient_id) REFERENCES usda_nutrient(id) ON DELETE CASCADE,
    UNIQUE KEY uk_nutrient_id (nutrient_id),
    INDEX idx_chinese_name (chinese_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA营养素中文翻译表';

-- 用户翻译贡献表
CREATE TABLE IF NOT EXISTS user_translation_contributions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    suggested_chinese_name VARCHAR(255) NOT NULL COMMENT '建议的中文名称',
    suggested_description TEXT COMMENT '建议的中文描述',
    status ENUM('待审核', '已采纳', '已拒绝') DEFAULT '待审核' COMMENT '状态',
    admin_feedback TEXT COMMENT '管理员反馈',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户翻译贡献表';

-- 创建中文化查询视图
CREATE OR REPLACE VIEW usda_foods_chinese AS
SELECT 
    f.fdc_id,
    f.data_type,
    f.description as english_name,
    COALESCE(ft.chinese_name, f.description) as chinese_name,
    COALESCE(ft.chinese_description, f.description) as chinese_description,
    f.food_category_id,
    fc.description as english_category,
    COALESCE(ct.chinese_name, fc.description) as chinese_category,
    f.publication_date,
    ft.translation_source,
    ft.confidence_score,
    ft.verified as translation_verified,
    f.create_time,
    f.update_time
FROM usda_foods f
LEFT JOIN usda_food_translations ft ON f.fdc_id = ft.fdc_id
LEFT JOIN usda_food_category fc ON f.food_category_id = fc.id
LEFT JOIN usda_category_translations ct ON fc.id = ct.category_id;

-- 创建营养素中文化视图
CREATE OR REPLACE VIEW usda_nutrients_chinese AS
SELECT 
    n.id,
    n.number,
    n.name as english_name,
    COALESCE(nt.chinese_name, n.name) as chinese_name,
    n.unit_name as english_unit,
    COALESCE(nt.chinese_unit, n.unit_name) as chinese_unit,
    nt.abbreviation as chinese_abbreviation,
    n.nutrient_nbr,
    n.`rank`,
    n.create_time,
    n.update_time
FROM usda_nutrient n
LEFT JOIN usda_nutrient_translations nt ON n.id = nt.nutrient_id;

-- 创建完整的中文化食物营养信息视图
CREATE OR REPLACE VIEW usda_food_nutrients_chinese AS
SELECT 
    fc.fdc_id,
    fc.chinese_name as food_name,
    fc.chinese_category as category,
    nc.chinese_name as nutrient_name,
    nc.chinese_unit as unit,
    fn.amount,
    fn.data_points,
    fn.min_value,
    fn.max_value,
    fn.median_value
FROM usda_foods_chinese fc
JOIN usda_food_nutrient fn ON fc.fdc_id = fn.fdc_id
JOIN usda_nutrients_chinese nc ON fn.nutrient_id = nc.id;

-- 插入基础营养素中文翻译
INSERT IGNORE INTO usda_nutrient_translations (nutrient_id, chinese_name, chinese_unit, abbreviation) VALUES
-- 基础营养素
(1008, '能量', 'kcal', '能量'),
(1003, '蛋白质', 'g', '蛋白质'),
(1004, '总脂肪', 'g', '脂肪'),
(1005, '碳水化合物', 'g', '碳水'),
(1079, '膳食纤维', 'g', '纤维'),
(1087, '钙', 'mg', '钙'),
(1089, '铁', 'mg', '铁'),
(1090, '镁', 'mg', '镁'),
(1091, '磷', 'mg', '磷'),
(1092, '钾', 'mg', '钾'),
(1093, '钠', 'mg', '钠'),
(1095, '锌', 'mg', '锌'),
(1098, '铜', 'mg', '铜'),
(1101, '锰', 'mg', '锰'),
(1103, '硒', 'μg', '硒'),
-- 维生素
(1106, '维生素A', 'μg', 'VA'),
(1162, '维生素C', 'mg', 'VC'),
(1109, '维生素E', 'mg', 'VE'),
(1114, '维生素D', 'μg', 'VD'),
(1183, '维生素K', 'μg', 'VK'),
(1165, '硫胺素(B1)', 'mg', 'B1'),
(1166, '核黄素(B2)', 'mg', 'B2'),
(1167, '烟酸', 'mg', '烟酸'),
(1175, '维生素B6', 'mg', 'B6'),
(1177, '叶酸', 'μg', '叶酸'),
(1178, '维生素B12', 'μg', 'B12'),
-- 其他重要成分
(1253, '胆固醇', 'mg', '胆固醇'),
(1258, '饱和脂肪酸', 'g', '饱和脂肪'),
(1292, '单不饱和脂肪酸', 'g', '单不饱和'),
(1293, '多不饱和脂肪酸', 'g', '多不饱和');

-- 插入基础食物分类中文翻译
INSERT IGNORE INTO usda_category_translations (category_id, chinese_name, chinese_description, translation_source) VALUES
(1, '乳制品和蛋制品', '包括牛奶、奶酪、酸奶、鸡蛋等', '手动'),
(2, '香料和调料', '各种香料、调味料和调味品', '手动'),
(3, '婴儿食品', '专为婴幼儿设计的食品', '手动'),
(4, '脂肪和油类', '各种食用油、黄油等脂肪类食品', '手动'),
(5, '禽肉制品', '鸡肉、鸭肉、火鸡肉等禽类肉制品', '手动'),
(6, '汤类、调味汁和肉汁', '各种汤品、调味汁和肉汁', '手动'),
(7, '香肠和午餐肉', '各种香肠、火腿、午餐肉等加工肉制品', '手动'),
(8, '早餐谷物', '各种早餐麦片、燕麦等谷物制品', '手动'),
(9, '水果和果汁', '新鲜水果、果汁和果制品', '手动'),
(10, '猪肉制品', '猪肉及各种猪肉制品', '手动'),
(11, '蔬菜和蔬菜制品', '新鲜蔬菜、蔬菜汁和蔬菜制品', '手动'),
(12, '坚果和种子制品', '各种坚果、种子及其制品', '手动'),
(13, '牛肉制品', '牛肉及各种牛肉制品', '手动'),
(14, '饮料', '各种饮料，不包括乳制品', '手动'),
(15, '鱼类和贝类制品', '鱼类、虾、蟹、贝类等海鲜制品', '手动'),
(16, '豆类和豆制品', '各种豆类、豆腐、豆浆等豆制品', '手动'),
(17, '羊肉、小牛肉和野味制品', '羊肉、小牛肉、鹿肉等制品', '手动'),
(18, '烘焙制品', '面包、蛋糕、饼干等烘焙食品', '手动'),
(19, '糖果', '各种糖果、巧克力等甜食', '手动'),
(20, '谷物、面食和面制品', '大米、面条、面粉等谷物制品', '手动'),
(21, '快餐', '各种快餐食品', '手动'),
(22, '餐厅食品', '餐厅制作的各种食品', '手动'),
(23, '零食', '各种休闲零食', '手动'),
(24, '美洲印第安/阿拉斯加原住民食品', '传统原住民食品', '手动'),
(25, '品牌食品', '各种品牌包装食品', '手动');
