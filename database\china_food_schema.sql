-- 中国食物成分表数据库架构
-- 基于《中国食物成分表标准版(第6版)》

USE meals;

-- 中国食物成分表主表
CREATE TABLE IF NOT EXISTS china_foods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    food_code VARCHAR(20) NOT NULL UNIQUE COMMENT '食物代码',
    food_name VARCHAR(255) NOT NULL COMMENT '食物名称',
    edible DECIMAL(5,2) COMMENT '可食部分(%)',
    water DECIMAL(8,3) COMMENT '水分(g)',
    energy_kcal INT COMMENT '能量(kcal)',
    energy_kj INT COMMENT '能量(kJ)',
    protein DECIMAL(8,3) COMMENT '蛋白质(g)',
    fat DECIMAL(8,3) COMMENT '脂肪(g)',
    cho DECIMAL(8,3) COMMENT '碳水化合物(g)',
    dietary_fiber DECIMAL(8,3) COMMENT '膳食纤维(g)',
    cholesterol DECIMAL(8,3) COMMENT '胆固醇(mg)',
    ash DECIMAL(8,3) COMMENT '灰分(g)',
    vitamin_a DECIMAL(8,3) COMMENT '维生素A(μg)',
    carotene DECIMAL(8,3) COMMENT '胡萝卜素(μg)',
    retinol DECIMAL(8,3) COMMENT '视黄醇(μg)',
    thiamin DECIMAL(8,4) COMMENT '硫胺素(mg)',
    riboflavin DECIMAL(8,4) COMMENT '核黄素(mg)',
    niacin DECIMAL(8,4) COMMENT '烟酸(mg)',
    vitamin_c DECIMAL(8,3) COMMENT '维生素C(mg)',
    vitamin_e_total DECIMAL(8,4) COMMENT '维生素E总量(mg)',
    vitamin_e1 DECIMAL(8,4) COMMENT 'α-维生素E(mg)',
    vitamin_e2 DECIMAL(8,4) COMMENT 'β-维生素E(mg)',
    vitamin_e3 DECIMAL(8,4) COMMENT 'γ-维生素E(mg)',
    ca DECIMAL(8,3) COMMENT '钙(mg)',
    p DECIMAL(8,3) COMMENT '磷(mg)',
    k DECIMAL(8,3) COMMENT '钾(mg)',
    na DECIMAL(8,3) COMMENT '钠(mg)',
    mg DECIMAL(8,3) COMMENT '镁(mg)',
    fe DECIMAL(8,4) COMMENT '铁(mg)',
    zn DECIMAL(8,4) COMMENT '锌(mg)',
    se DECIMAL(8,4) COMMENT '硒(μg)',
    cu DECIMAL(8,4) COMMENT '铜(mg)',
    mn DECIMAL(8,4) COMMENT '锰(mg)',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_food_code (food_code),
    INDEX idx_food_name (food_name),
    FULLTEXT INDEX ft_food_name (food_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='中国食物成分表';

-- 中国食物分类表
CREATE TABLE IF NOT EXISTS china_food_categories (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    category_code VARCHAR(20) NOT NULL UNIQUE COMMENT '分类代码',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id INT COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_code (category_code),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES china_food_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='中国食物分类表';

-- 食物升糖指数表
CREATE TABLE IF NOT EXISTS china_food_gi (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    food_name VARCHAR(255) NOT NULL COMMENT '食物名称',
    gi_value DECIMAL(5,2) COMMENT 'GI值',
    gi_level ENUM('低', '中', '高') COMMENT 'GI等级',
    serving_size VARCHAR(50) COMMENT '份量',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_food_name (food_name),
    INDEX idx_gi_level (gi_level),
    FULLTEXT INDEX ft_food_name (food_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='食物升糖指数表';

-- 用户收藏的中国食物表
CREATE TABLE IF NOT EXISTS user_favorite_china_foods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    china_food_id BIGINT NOT NULL COMMENT '中国食物ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (china_food_id) REFERENCES china_foods(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_china_food (user_id, china_food_id),
    INDEX idx_user_id (user_id),
    INDEX idx_china_food_id (china_food_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏中国食物表';

-- 中国食物与USDA食物映射表
CREATE TABLE IF NOT EXISTS china_usda_food_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    china_food_id BIGINT NOT NULL COMMENT '中国食物ID',
    usda_fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    similarity_score DECIMAL(3,2) COMMENT '相似度评分(0-1)',
    mapping_type ENUM('自动', '手动', '验证') DEFAULT '自动' COMMENT '映射类型',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (china_food_id) REFERENCES china_foods(id) ON DELETE CASCADE,
    FOREIGN KEY (usda_fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_china_food_id (china_food_id),
    INDEX idx_usda_fdc_id (usda_fdc_id),
    INDEX idx_similarity (similarity_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='中国食物与USDA食物映射表';

-- 创建统一食物查询视图
CREATE OR REPLACE VIEW unified_food_view AS
SELECT 
    'china' as data_source,
    cf.id as source_id,
    cf.food_code as code,
    cf.food_name as name,
    cf.energy_kcal as energy,
    cf.protein,
    cf.fat,
    cf.cho as carbohydrate,
    cf.dietary_fiber,
    cf.ca as calcium,
    cf.fe as iron,
    cf.vitamin_c,
    cf.create_time
FROM china_foods cf
UNION ALL
SELECT 
    'usda' as data_source,
    uf.fdc_id as source_id,
    CAST(uf.fdc_id AS CHAR) as code,
    uf.description as name,
    NULL as energy,  -- 需要从food_nutrient表关联获取
    NULL as protein,
    NULL as fat,
    NULL as carbohydrate,
    NULL as dietary_fiber,
    NULL as calcium,
    NULL as iron,
    NULL as vitamin_c,
    uf.create_time
FROM usda_foods uf;

-- 插入基础食物分类数据
INSERT IGNORE INTO china_food_categories (category_code, category_name, sort_order) VALUES
('01', '谷类及其制品', 1),
('02', '薯类、淀粉及其制品', 2),
('03', '干豆类及其制品', 3),
('04', '蔬菜类及其制品', 4),
('05', '菌藻类', 5),
('06', '水果类及其制品', 6),
('07', '坚果、种子类', 7),
('08', '畜肉类及其制品', 8),
('09', '禽肉类及其制品', 9),
('10', '乳类及其制品', 10),
('11', '蛋类及其制品', 11),
('12', '鱼虾蟹贝类', 12),
('13', '调料类', 13),
('14', '饮料类', 14),
('15', '酒类', 15),
('16', '其他', 16);
