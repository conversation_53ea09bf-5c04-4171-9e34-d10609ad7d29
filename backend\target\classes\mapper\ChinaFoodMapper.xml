<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.ChinaFoodMapper">

    <!-- 结果映射 -->
    <resultMap id="ChinaFoodResultMap" type="com.example.meals.entity.ChinaFood">
        <id column="id" property="id" />
        <result column="food_code" property="foodCode" />
        <result column="food_name" property="foodName" />
        <result column="edible" property="edible" />
        <result column="water" property="water" />
        <result column="energy_kcal" property="energyKcal" />
        <result column="energy_kj" property="energyKj" />
        <result column="protein" property="protein" />
        <result column="fat" property="fat" />
        <result column="cho" property="cho" />
        <result column="dietary_fiber" property="dietaryFiber" />
        <result column="cholesterol" property="cholesterol" />
        <result column="ash" property="ash" />
        <result column="vitamin_a" property="vitaminA" />
        <result column="carotene" property="carotene" />
        <result column="retinol" property="retinol" />
        <result column="thiamin" property="thiamin" />
        <result column="riboflavin" property="riboflavin" />
        <result column="niacin" property="niacin" />
        <result column="vitamin_c" property="vitaminC" />
        <result column="vitamin_e_total" property="vitaminETotal" />
        <result column="vitamin_e1" property="vitaminE1" />
        <result column="vitamin_e2" property="vitaminE2" />
        <result column="vitamin_e3" property="vitaminE3" />
        <result column="ca" property="ca" />
        <result column="p" property="p" />
        <result column="k" property="k" />
        <result column="na" property="na" />
        <result column="mg" property="mg" />
        <result column="fe" property="fe" />
        <result column="zn" property="zn" />
        <result column="se" property="se" />
        <result column="cu" property="cu" />
        <result column="mn" property="mn" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据食物名称搜索食物（模糊匹配） -->
    <select id="searchByName" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods 
        WHERE food_name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY 
        CASE 
          WHEN food_name = #{keyword} THEN 1 
          WHEN food_name LIKE CONCAT(#{keyword}, '%') THEN 2 
          WHEN food_name LIKE CONCAT('%', #{keyword}) THEN 3 
          ELSE 4 
        END, 
        LENGTH(food_name) ASC 
        LIMIT #{limit}
    </select>

    <!-- 获取所有食物分类 -->
    <select id="getAllCategories" resultType="string">
        SELECT DISTINCT SUBSTRING(food_code, 1, 2) as category_code
        FROM china_foods
        WHERE food_code IS NOT NULL AND food_code != '' AND LENGTH(food_code) >= 2
        ORDER BY category_code
    </select>

    <!-- 根据分类获取食物列表 -->
    <select id="getFoodsByCategory" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods 
        WHERE food_code LIKE CONCAT(#{categoryCode}, '%') 
        ORDER BY food_name 
        LIMIT #{limit}
    </select>

    <!-- 获取热门食物 -->
    <select id="getPopularFoods" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods 
        WHERE energy_kcal IS NOT NULL 
        AND food_name NOT LIKE '%，%' 
        AND food_name NOT LIKE '%（%' 
        ORDER BY 
        CASE 
          WHEN food_name IN ('大米', '小麦粉', '鸡蛋', '牛奶', '猪肉', '鸡肉', '苹果', '香蕉', '白菜', '土豆') THEN 1 
          ELSE 2 
        END, 
        energy_kcal DESC 
        LIMIT #{limit}
    </select>

    <!-- 根据ID获取食物详情 -->
    <select id="getFoodById" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods WHERE id = #{id}
    </select>

</mapper>
