package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodSearchResponse;
import com.example.meals.dto.NutritionResponse;
import com.example.meals.entity.ChinaFood;
import com.example.meals.mapper.ChinaFoodMapper;
import com.example.meals.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 食物服务实现类
 */
@Service
public class FoodServiceImpl implements FoodService {
    
    @Autowired
    private ChinaFoodMapper chinaFoodMapper;
    
    @Override
    public Result<List<FoodSearchResponse>> searchFoods(String keyword, Integer limit) {
        try {
            // 参数验证
            if (!StringUtils.hasText(keyword)) {
                return Result.badRequest("搜索关键词不能为空");
            }
            
            if (keyword.trim().length() < 2) {
                return Result.badRequest("搜索关键词至少需要2个字符");
            }
            
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            if (limit > 50) {
                limit = 50; // 最大限制50条
            }
            
            // 搜索食物
            List<ChinaFood> foods = chinaFoodMapper.searchByName(keyword.trim(), limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(FoodSearchResponse::new)
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("搜索食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<String>> getAllCategories() {
        try {
            List<String> categoryCodes = chinaFoodMapper.getAllCategories();
            
            // 转换为分类名称
            List<String> categoryNames = categoryCodes.stream()
                    .map(this::getCategoryName)
                    .collect(Collectors.toList());
            
            return Result.success(categoryNames);
            
        } catch (Exception e) {
            return Result.error("获取食物分类失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<FoodSearchResponse>> getFoodsByCategory(String categoryCode, Integer limit) {
        try {
            // 参数验证
            if (!StringUtils.hasText(categoryCode)) {
                return Result.badRequest("分类代码不能为空");
            }
            
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 20;
            }
            if (limit > 100) {
                limit = 100; // 最大限制100条
            }
            
            // 获取分类食物
            List<ChinaFood> foods = chinaFoodMapper.getFoodsByCategory(categoryCode, limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(FoodSearchResponse::new)
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("获取分类食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<FoodSearchResponse>> getPopularFoods(Integer limit) {
        try {
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 6;
            }
            if (limit > 20) {
                limit = 20; // 最大限制20条
            }
            
            // 获取热门食物
            List<ChinaFood> foods = chinaFoodMapper.getPopularFoods(limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(FoodSearchResponse::new)
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("获取热门食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<NutritionResponse> analyzeNutrition(Long foodId, Integer weight) {
        try {
            // 参数验证
            if (foodId == null || foodId <= 0) {
                return Result.badRequest("食物ID不能为空");
            }
            
            // 设置默认重量
            if (weight == null || weight <= 0) {
                weight = 100;
            }
            if (weight > 10000) {
                weight = 10000; // 最大限制10kg
            }
            
            // 获取食物详情
            ChinaFood food = chinaFoodMapper.getFoodById(foodId);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            
            // 创建营养分析响应
            NutritionResponse response = new NutritionResponse(food, weight);
            
            return Result.success(response);
            
        } catch (Exception e) {
            return Result.error("营养分析失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<FoodSearchResponse> getFoodById(Long foodId) {
        try {
            // 参数验证
            if (foodId == null || foodId <= 0) {
                return Result.badRequest("食物ID不能为空");
            }
            
            // 获取食物详情
            ChinaFood food = chinaFoodMapper.getFoodById(foodId);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            
            // 转换为响应DTO
            FoodSearchResponse response = new FoodSearchResponse(food);
            
            return Result.success(response);
            
        } catch (Exception e) {
            return Result.error("获取食物详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据分类代码获取分类名称
     */
    private String getCategoryName(String categoryCode) {
        switch (categoryCode) {
            case "01": return "谷类及其制品";
            case "02": return "薯类、淀粉及其制品";
            case "03": return "干豆类及其制品";
            case "04": return "蔬菜类及其制品";
            case "05": return "菌藻类";
            case "06": return "水果类及其制品";
            case "07": return "坚果、种子类";
            case "08": return "畜肉类及其制品";
            case "09": return "禽肉类及其制品";
            case "10": return "乳类及其制品";
            case "11": return "蛋类及其制品";
            case "12": return "鱼虾蟹贝类";
            case "13": return "调料类";
            case "14": return "饮料类";
            case "15": return "酒类";
            case "16": return "其他";
            default: return "未知分类";
        }
    }
}
