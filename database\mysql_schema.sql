
USE meals;

-- USDA食物表 (主表) - 集成到meals数据库
CREATE TABLE IF NOT EXISTS usda_foods (
    fdc_id INT PRIMARY KEY COMMENT 'USDA食物数据中心ID',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    description TEXT NOT NULL COMMENT '食物描述',
    food_category_id INT COMMENT '食物分类ID',
    publication_date DATE COMMENT '发布日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_data_type (data_type),
    INDEX idx_food_category (food_category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物主表';

-- USDA食物类别表
CREATE TABLE IF NOT EXISTS usda_food_category (
    id INT PRIMARY KEY COMMENT '分类ID',
    code VARCHAR(10) COMMENT '分类代码',
    description VARCHAR(255) NOT NULL COMMENT '分类描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物分类表';

-- USDA营养素表
CREATE TABLE IF NOT EXISTS usda_nutrient (
    id INT PRIMARY KEY COMMENT '营养素ID',
    number VARCHAR(10) COMMENT '营养素编号',
    name VARCHAR(255) NOT NULL COMMENT '营养素名称',
    unit_name VARCHAR(50) COMMENT '单位名称',
    nutrient_nbr DECIMAL(10,3) COMMENT '营养素序号',
    `rank` INT COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_number (number),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA营养素定义表';

-- USDA食物营养素含量表 (核心数据表)
CREATE TABLE IF NOT EXISTS usda_food_nutrient (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    nutrient_id INT NOT NULL COMMENT '营养素ID',
    amount DECIMAL(15,6) COMMENT '营养素含量',
    data_points INT COMMENT '数据点数',
    derivation_id INT COMMENT '推导ID',
    min_value DECIMAL(15,6) COMMENT '最小值',
    max_value DECIMAL(15,6) COMMENT '最大值',
    median_value DECIMAL(15,6) COMMENT '中位数',
    footnote_id INT COMMENT '脚注ID',
    min_year_acquired INT COMMENT '最小获取年份',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    FOREIGN KEY (nutrient_id) REFERENCES usda_nutrient(id),
    INDEX idx_fdc_nutrient (fdc_id, nutrient_id),
    INDEX idx_nutrient (nutrient_id),
    INDEX idx_amount (amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物营养素含量表';

-- USDA食物属性表
CREATE TABLE IF NOT EXISTS usda_food_attribute (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    seq_num INT COMMENT '序号',
    food_attribute_type_id INT COMMENT '属性类型ID',
    name VARCHAR(255) COMMENT '属性名称',
    value VARCHAR(255) COMMENT '属性值',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_type (food_attribute_type_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物属性表';

-- USDA食物属性类型表
CREATE TABLE IF NOT EXISTS usda_food_attribute_type (
    id INT PRIMARY KEY COMMENT '属性类型ID',
    name VARCHAR(255) NOT NULL COMMENT '属性类型名称',
    description TEXT COMMENT '属性类型描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物属性类型表';

-- USDA品牌食品表 (扩展信息)
CREATE TABLE IF NOT EXISTS usda_branded_food (
    fdc_id INT PRIMARY KEY COMMENT 'USDA食物ID',
    brand_owner VARCHAR(255) COMMENT '品牌所有者',
    brand_name VARCHAR(255) COMMENT '品牌名称',
    subbrand_name VARCHAR(255) COMMENT '子品牌名称',
    gtin_upc VARCHAR(50) COMMENT '全球贸易项目代码/通用产品代码',
    ingredients TEXT COMMENT '配料表',
    not_a_significant_source_of TEXT COMMENT '非重要来源',
    serving_size DECIMAL(10,3) COMMENT '份量大小',
    serving_size_unit VARCHAR(50) COMMENT '份量单位',
    household_serving_fulltext VARCHAR(255) COMMENT '家庭份量全文',
    branded_food_category VARCHAR(255) COMMENT '品牌食品分类',
    data_source VARCHAR(100) COMMENT '数据来源',
    package_weight VARCHAR(50) COMMENT '包装重量',
    modified_date DATE COMMENT '修改日期',
    available_date DATE COMMENT '可用日期',
    market_country VARCHAR(100) COMMENT '市场国家',
    discontinued_date DATE COMMENT '停产日期',
    preparation_state_code VARCHAR(50) COMMENT '制备状态代码',
    trade_channel VARCHAR(100) COMMENT '贸易渠道',
    short_description TEXT COMMENT '简短描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_brand_owner (brand_owner),
    INDEX idx_gtin_upc (gtin_upc),
    INDEX idx_category (branded_food_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA品牌食品表';

-- USDA基础食品表 (Foundation Foods)
CREATE TABLE IF NOT EXISTS usda_foundation_food (
    fdc_id INT PRIMARY KEY COMMENT 'USDA食物ID',
    NDB_number INT COMMENT 'NDB编号',
    footnote_id INT COMMENT '脚注ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_ndb_number (NDB_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA基础食品表';

-- USDA SR Legacy食品表
CREATE TABLE IF NOT EXISTS usda_sr_legacy_food (
    fdc_id INT PRIMARY KEY COMMENT 'USDA食物ID',
    NDB_number INT COMMENT 'NDB编号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_ndb_number (NDB_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA SR Legacy食品表';

-- USDA调查食品表 (FNDDS)
CREATE TABLE IF NOT EXISTS usda_survey_fndds_food (
    fdc_id INT PRIMARY KEY COMMENT 'USDA食物ID',
    food_code INT COMMENT '食物代码',
    wweia_category_code INT COMMENT 'WWEIA分类代码',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_food_code (food_code),
    INDEX idx_wweia_category (wweia_category_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA调查食品表';

-- USDA测量单位表
CREATE TABLE IF NOT EXISTS usda_measure_unit (
    id INT PRIMARY KEY COMMENT '单位ID',
    name VARCHAR(100) NOT NULL COMMENT '单位名称',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA测量单位表';

-- USDA食物分量表
CREATE TABLE IF NOT EXISTS usda_food_portion (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    seq_num INT COMMENT '序号',
    amount DECIMAL(10,3) COMMENT '数量',
    measure_unit_id INT COMMENT '测量单位ID',
    portion_description VARCHAR(255) COMMENT '分量描述',
    modifier VARCHAR(255) COMMENT '修饰符',
    gram_weight DECIMAL(10,3) COMMENT '克重',
    data_points INT COMMENT '数据点数',
    footnote_id INT COMMENT '脚注ID',
    min_year_acquired INT COMMENT '最小获取年份',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    FOREIGN KEY (measure_unit_id) REFERENCES usda_measure_unit(id),
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_measure_unit (measure_unit_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物分量表';

-- USDA营养素推导表
CREATE TABLE IF NOT EXISTS usda_nutrient_incoming_name (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    name VARCHAR(255) NOT NULL COMMENT '营养素名称',
    nutrient_id INT COMMENT '营养素ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (nutrient_id) REFERENCES usda_nutrient(id),
    INDEX idx_name (name),
    INDEX idx_nutrient_id (nutrient_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA营养素推导表';

-- USDA食物更新日志表
CREATE TABLE IF NOT EXISTS usda_food_update_log_entry (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    available_date DATE COMMENT '可用日期',
    brand_owner VARCHAR(255) COMMENT '品牌所有者',
    data_source VARCHAR(100) COMMENT '数据来源',
    data_type VARCHAR(50) COMMENT '数据类型',
    description TEXT COMMENT '描述',
    food_class VARCHAR(100) COMMENT '食物类别',
    gtinUpc VARCHAR(50) COMMENT 'GTIN/UPC代码',
    household_serving_fulltext VARCHAR(255) COMMENT '家庭份量全文',
    ingredients TEXT COMMENT '配料',
    modified_date DATE COMMENT '修改日期',
    publication_date DATE COMMENT '发布日期',
    serving_size DECIMAL(10,3) COMMENT '份量大小',
    serving_size_unit VARCHAR(50) COMMENT '份量单位',
    branded_food_category VARCHAR(255) COMMENT '品牌食品分类',
    changes VARCHAR(500) COMMENT '变更内容',
    food_attributes TEXT COMMENT '食物属性',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_data_type (data_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDA食物更新日志表';

-- 创建外键约束
ALTER TABLE usda_foods ADD FOREIGN KEY (food_category_id) REFERENCES usda_food_category(id);
ALTER TABLE usda_food_attribute ADD FOREIGN KEY (food_attribute_type_id) REFERENCES usda_food_attribute_type(id);

-- 创建常用查询的视图
CREATE OR REPLACE VIEW usda_food_with_nutrients AS
SELECT
    f.fdc_id,
    f.description as food_description,
    f.data_type,
    fc.description as category,
    n.name as nutrient_name,
    n.unit_name,
    fn.amount as nutrient_amount
FROM usda_foods f
LEFT JOIN usda_food_category fc ON f.food_category_id = fc.id
LEFT JOIN usda_food_nutrient fn ON f.fdc_id = fn.fdc_id
LEFT JOIN usda_nutrient n ON fn.nutrient_id = n.id;

-- 创建品牌食品详细视图
CREATE OR REPLACE VIEW usda_branded_food_details AS
SELECT
    f.fdc_id,
    f.description,
    bf.brand_owner,
    bf.brand_name,
    bf.gtin_upc,
    bf.ingredients,
    bf.serving_size,
    bf.serving_size_unit,
    bf.branded_food_category
FROM usda_foods f
JOIN usda_branded_food bf ON f.fdc_id = bf.fdc_id
WHERE f.data_type = 'branded_food';

-- 插入一些基础数据
INSERT IGNORE INTO usda_measure_unit (id, name) VALUES
(1, 'g'), (2, 'mg'), (3, 'µg'), (4, 'ml'), (5, 'cup'),
(6, 'tbsp'), (7, 'tsp'), (8, 'oz'), (9, 'lb'), (10, 'piece');

-- 创建索引以优化查询性能
CREATE INDEX idx_usda_foods_description_fulltext ON usda_foods(description(255));
CREATE INDEX idx_usda_branded_food_ingredients_fulltext ON usda_branded_food(ingredients(255));
CREATE INDEX idx_usda_food_nutrient_composite ON usda_food_nutrient(fdc_id, nutrient_id, amount);

-- 创建与现有业务表的关联表 (可选)
-- 用户收藏的USDA食物表
CREATE TABLE IF NOT EXISTS user_favorite_usda_foods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_food (user_id, fdc_id),
    INDEX idx_user_id (user_id),
    INDEX idx_fdc_id (fdc_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏USDA食物表';

-- 用户自定义食物与USDA食物的关联表
CREATE TABLE IF NOT EXISTS user_food_usda_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    user_food_id BIGINT COMMENT '用户自定义食物ID',
    fdc_id INT NOT NULL COMMENT 'USDA食物ID',
    similarity_score DECIMAL(3,2) COMMENT '相似度评分(0-1)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (fdc_id) REFERENCES usda_foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_user_food_id (user_food_id),
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_similarity (similarity_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户食物与USDA食物映射表';
